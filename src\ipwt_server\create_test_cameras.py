#!/usr/bin/env python
"""
创建测试摄像头数据的脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ipwtbackend.settings')
django.setup()

from base.models import Camera

def create_test_cameras():
    """创建测试摄像头数据"""
    
    print("开始创建测试摄像头数据...")
    
    # 测试数据
    test_cameras = [
        {
            "name": "主摄像头-001",
            "camera_type": "main",
            "brand": "Hikvision",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "主要监控摄像头"
        },
        {
            "name": "主摄像头-002",
            "camera_type": "main",
            "brand": "Dahua",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "备用主摄像头"
        },
        {
            "name": "驾驶室摄像头-001",
            "camera_type": "cockpit",
            "brand": "Hikvision",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "驾驶室内部监控"
        },
        {
            "name": "驾驶室摄像头-002",
            "camera_type": "cockpit",
            "brand": "Dahua",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "驾驶室外部监控"
        },
        {
            "name": "其他摄像头-001",
            "camera_type": "other",
            "brand": "Hikvision",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "侧面监控摄像头"
        },
        {
            "name": "其他摄像头-002",
            "camera_type": "other",
            "brand": "Dahua",
            "ip": "*************",
            "port": 554,
            "username": "admin",
            "password": "admin123",
            "channel": "1",
            "stream_type": "0",
            "remarks": "后方监控摄像头"
        }
    ]
    
    created_count = 0
    for camera_data in test_cameras:
        # 检查是否已存在相同名称的摄像头
        if not Camera.objects.filter(name=camera_data['name']).exists():
            camera = Camera.objects.create(**camera_data)
            print(f"创建摄像头: {camera.name} - RTSP: {camera.rtsp_url}")
            created_count += 1
        else:
            print(f"摄像头已存在: {camera_data['name']}")
    
    print(f"\n创建完成! 共创建了 {created_count} 个摄像头")
    print(f"数据库中现有摄像头总数: {Camera.objects.count()}")

if __name__ == "__main__":
    create_test_cameras()
