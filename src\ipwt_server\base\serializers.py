from rest_framework import serializers
from .models import Camera


class CameraSerializer(serializers.ModelSerializer):
    """摄像头完整信息序列化器"""
    
    class Meta:
        model = Camera
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class CameraListSerializer(serializers.ModelSerializer):
    """摄像头列表序列化器 - 用于API接口返回"""
    
    camera_type_display = serializers.CharField(source='get_camera_type_display', read_only=True)
    
    class Meta:
        model = Camera
        fields = ['id', 'name', 'camera_type', 'camera_type_display', 'rtsp_url']


class CameraCreateUpdateSerializer(serializers.ModelSerializer):
    """摄像头创建和更新序列化器"""
    
    class Meta:
        model = Camera
        fields = [
            'name', 'camera_type', 'brand', 'ip', 'port', 
            'username', 'password', 'channel', 'stream_type', 
            'rtsp_url', 'remarks'
        ]
        extra_kwargs = {
            'password': {'write_only': True},  # 密码字段只写不读
        }
    
    def validate_name(self, value):
        """验证相机名称不能为空"""
        if not value or not value.strip():
            raise serializers.ValidationError("相机名称不能为空")
        return value.strip()
    
    def validate_rtsp_url(self, value):
        """验证RTSP地址不能为空"""
        if not value or not value.strip():
            raise serializers.ValidationError("RTSP地址不能为空")
        return value.strip()
    
    def validate(self, attrs):
        """整体验证"""
        # 如果没有提供RTSP地址，尝试自动生成
        if not attrs.get('rtsp_url'):
            # 检查是否有足够的信息生成RTSP地址
            required_fields = ['brand', 'ip', 'port', 'username', 'password', 'channel']
            if all(attrs.get(field) for field in required_fields):
                # 创建临时Camera实例来生成RTSP地址
                temp_camera = Camera(**attrs)
                attrs['rtsp_url'] = temp_camera.generate_rtsp_url()
        
        return attrs
