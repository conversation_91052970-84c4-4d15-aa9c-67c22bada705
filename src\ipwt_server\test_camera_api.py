#!/usr/bin/env python
"""
摄像头API接口测试脚本
测试新创建的摄像头相关API接口
"""

import requests
import json
from pprint import pprint

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

def test_api_endpoints():
    """测试所有摄像头API接口"""

    print("=" * 60)
    print("摄像头API接口测试")
    print("=" * 60)

    # 1. 测试查询主摄像头列表
    print("\n1. 测试查询主摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/main-cam/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 2. 测试查询驾驶室摄像头列表
    print("\n2. 测试查询驾驶室摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/cockpit-cams/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")

    # 3. 测试查询其他摄像头列表
    print("\n3. 测试查询其他摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/other-cams/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")

    # 4. 测试查询所有摄像头
    print("\n4. 测试查询所有摄像头...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/cameras/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    test_api_endpoints()
