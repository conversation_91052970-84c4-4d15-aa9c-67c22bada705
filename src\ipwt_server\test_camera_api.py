#!/usr/bin/env python
"""
摄像头API接口测试脚本
测试新创建的摄像头相关API接口
"""

import requests
import json
from pprint import pprint

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

def test_api_endpoints():
    """测试所有摄像头API接口"""
    
    print("=" * 60)
    print("摄像头API接口测试")
    print("=" * 60)
    
    # 测试数据
    test_camera_data = {
        "name": "测试主摄像头",
        "camera_type": "main",
        "brand": "Hikvision",
        "ip": "*************",
        "port": 554,
        "username": "admin",
        "password": "admin123",
        "channel": "1",
        "stream_type": "0",
        "remarks": "这是一个测试摄像头"
    }
    
    # 1. 测试创建摄像头
    print("\n1. 测试创建摄像头...")
    try:
        response = requests.post(
            f"{BASE_URL}/v1/api/cameras/",
            json=test_camera_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 201:
            camera_data = response.json()
            print("创建成功!")
            pprint(camera_data)
            camera_id = camera_data.get('id')
        else:
            print("创建失败:")
            print(response.text)
            return
    except Exception as e:
        print(f"请求失败: {e}")
        return
    
    # 2. 测试查询主摄像头列表
    print("\n2. 测试查询主摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/main-cam/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 3. 测试查询驾驶室摄像头列表
    print("\n3. 测试查询驾驶室摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/cockpit-cams/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 4. 测试查询其他摄像头列表
    print("\n4. 测试查询其他摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/other-cams/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 5. 测试查询所有摄像头
    print("\n5. 测试查询所有摄像头...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/cameras/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 6. 创建驾驶室摄像头用于测试
    print("\n6. 创建驾驶室摄像头用于测试...")
    cockpit_camera_data = {
        "name": "测试驾驶室摄像头",
        "camera_type": "cockpit",
        "brand": "Dahua",
        "ip": "*************",
        "port": 554,
        "username": "admin",
        "password": "admin123",
        "channel": "1",
        "stream_type": "0",
        "remarks": "这是一个测试驾驶室摄像头"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/api/cameras/",
            json=cockpit_camera_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 201:
            print("驾驶室摄像头创建成功!")
        else:
            print("创建失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 7. 再次测试查询驾驶室摄像头列表
    print("\n7. 再次测试查询驾驶室摄像头列表...")
    try:
        response = requests.get(f"{BASE_URL}/v1/api/cockpit-cams/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("查询成功!")
            pprint(data)
        else:
            print("查询失败:")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    test_api_endpoints()
