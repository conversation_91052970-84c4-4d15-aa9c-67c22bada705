from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器并注册ViewSet
router = DefaultRouter()
router.register(r'cameras', views.CameraViewSet, basename='camera')

urlpatterns = [
    # 版本化API路由
    # 查询主摄像头信息API接口
    path('main-cam/', views.main_camera_list, name='main-camera-list'),
    
    # 查询驾驶室摄像头信息API接口
    path('cockpit-cams/', views.cockpit_camera_list, name='cockpit-camera-list'),
    
    # 查询其他摄像头信息API接口
    path('other-cams/', views.other_camera_list, name='other-camera-list'),
    
    # RESTful API路由 - 用于WEB页面的CRUD操作
    path('', include(router.urls)),
]
