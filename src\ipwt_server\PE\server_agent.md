2025.7.29

**交流约束**

- 从现在开始请用中文跟我交流
- 你是一个智能助手，请务必把用户的问题彻底解决完，然后再结束对话并让用户继续操作。只有当你确定问题完全解决了，才能结束对话。
- 如果对用户发的的文件内容或代码结构不确定，一定要用你的工具去读取文件并收集相关信息，绝对不要瞎猜或编造答案。
- 你在每次函数调用之前，必须进行全面、细致、清楚的规划，并周全考虑之前函数调用的结果。不要通过只调用函数来完成整个过程，这样会损害你解决问题和深入思考的能力。

**正文**：

我需要搭建一个django后端服务

## 具体要求

- django==5.2.4

- 支持restful风格的api

- 支持用户登录，基于角色的权限管理

- 使用postgresql数据库

- 需要支持GDAL

  ```
  conda install -c conda-forge gdal
  ```

  ```python
  import os
  GDAL_LIBRARY_PATH = r'E:\conda\venvs\ipwt-server\Library\bin\gdal304.dll'  # 根据您的实际路径调整
  ```

其他说明

我已经执行了如下命令，创建了一个django后端目录

```sh
django-admin startproject ipwtbackend ipwt_server
```

----

02

## 规范本服务的接口url格式

### 接口带版本信息

例如：v1, v2

示例 /v1/api/maincam

## base 模块中添加摄像头基本信息相关功能

### 模型字段描述

| 序号 | 字段     | 空判断   | 字段类型 | 最大长度 | 备注                                                         |
| ---- | -------- | -------- | -------- | -------- | ------------------------------------------------------------ |
| 1    | 相机名称 | 不能为空 | str      | 32       |                                                              |
| 2    | 相机类型 |          | str      |          | - 主<br />- 驾驶室<br />- 其他                               |
| 3    | 相机品牌 |          | str      |          | - Hikvision                                                  |
| 4    | ip       |          | str      | 256      | - Dahua                                                      |
| 5    | 端口     |          | int      |          |                                                              |
| 6    | 账号     |          | str      | 128      |                                                              |
| 7    | 密码     |          | str      | 128      |                                                              |
| 8    | 通道号   |          | str      | 128      |                                                              |
| 9    | 码流类型 |          | str      | 128      |                                                              |
| 10   | rtsp地址 | 不能为空 | str      | 512      | 根据品牌，ip,端口，账号，密码，通道号，码流类型拼接而成，见“拼接规则” |
| 11   | 备注     |          | str      |          |                                                              |



### 功能

#### 查询相机基本信息的API接口

##### 查询主摄像头信息API接口

url：  /v1/api/main-cam

根据条件“相机类型：主” 为条件，查询rtsp不为空的相机信息，返回list, 相机名称，类型，rtsp地址

##### 查询驾驶室摄像头信息API接口

url:  /v1/api/cockpit-cams

根据条件“相机类型：驾驶室” 为条件，查询rtsp不为空的相机信息，返回list, 相机名称，类型，rtsp地址

##### 查询其他摄像头信息API接口

url:  /v1/api/other-cams

根据条件“相机类型：其他” 为条件，查询rtsp不为空的相机信息，返回list, 相机名称，类型，rtsp地址



WEB页面查询摄像头信息的restful接口
