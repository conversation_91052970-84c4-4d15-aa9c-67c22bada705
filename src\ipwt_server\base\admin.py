from django.contrib import admin
from .models import Camera


@admin.register(Camera)
class CameraAdmin(admin.ModelAdmin):
    """摄像头管理界面配置"""

    list_display = [
        'name', 'camera_type', 'brand', 'ip', 'port',
        'rtsp_url', 'created_at', 'updated_at'
    ]

    list_filter = [
        'camera_type', 'brand', 'created_at'
    ]

    search_fields = [
        'name', 'ip', 'rtsp_url', 'remarks'
    ]

    readonly_fields = [
        'created_at', 'updated_at'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'camera_type', 'brand')
        }),
        ('连接信息', {
            'fields': ('ip', 'port', 'username', 'password', 'channel', 'stream_type')
        }),
        ('RTSP配置', {
            'fields': ('rtsp_url',)
        }),
        ('其他信息', {
            'fields': ('remarks',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ['-created_at']

    def save_model(self, request, obj, form, change):
        """保存时自动生成RTSP地址"""
        if not obj.rtsp_url and obj.can_generate_rtsp():
            obj.rtsp_url = obj.generate_rtsp_url()
        super().save_model(request, obj, form, change)
