from rest_framework import viewsets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from .models import Camera
from .serializers import (
    CameraSerializer,
    ********************,
    CameraCreateUpdateSerializer
)


class CameraViewSet(viewsets.ModelViewSet):
    """摄像头管理ViewSet - 提供完整的CRUD操作"""

    queryset = Camera.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['camera_type', 'brand']
    search_fields = ['name', 'ip', 'remarks']
    ordering_fields = ['created_at', 'name']
    ordering = ['-created_at']

    def get_permissions(self):
        """根据操作类型返回不同的权限"""
        if self.action in ['list', 'retrieve']:
            # 查询操作允许匿名访问
            permission_classes = [AllowAny]
        else:
            # 创建、更新、删除操作需要认证
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        """根据操作类型返回不同的序列化器"""
        if self.action in ['create', 'update', 'partial_update']:
            return CameraCreateUpdateSerializer
        elif self.action == 'list':
            return ********************
        return CameraSerializer


@api_view(['GET'])
@permission_classes([AllowAny])
def main_camera_list(request):
    """查询主摄像头信息API接口

    URL: /v1/api/main-cam
    根据条件"相机类型：主"为条件，查询rtsp不为空的相机信息
    返回list: 相机名称，类型，rtsp地址
    """
    cameras = Camera.objects.filter(
        camera_type='main',
        rtsp_url__isnull=False
    ).exclude(rtsp_url='')

    serializer = ********************(cameras, many=True)
    return Response({
        'success': True,
        'data': serializer.data,
        'count': cameras.count()
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def cockpit_camera_list(request):
    """查询驾驶室摄像头信息API接口

    URL: /v1/api/cockpit-cams
    根据条件"相机类型：驾驶室"为条件，查询rtsp不为空的相机信息
    返回list: 相机名称，类型，rtsp地址
    """
    cameras = Camera.objects.filter(
        camera_type='cockpit',
        rtsp_url__isnull=False
    ).exclude(rtsp_url='')

    serializer = ********************(cameras, many=True)
    return Response({
        'success': True,
        'data': serializer.data,
        'count': cameras.count()
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def other_camera_list(request):
    """查询其他摄像头信息API接口

    URL: /v1/api/other-cams
    根据条件"相机类型：其他"为条件，查询rtsp不为空的相机信息
    返回list: 相机名称，类型，rtsp地址
    """
    cameras = Camera.objects.filter(
        camera_type='other',
        rtsp_url__isnull=False
    ).exclude(rtsp_url='')

    serializer = ********************(cameras, many=True)
    return Response({
        'success': True,
        'data': serializer.data,
        'count': cameras.count()
    })
