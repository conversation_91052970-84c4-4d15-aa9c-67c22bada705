from rest_framework import permissions

class RoleBasedPermission(permissions.BasePermission):
    """基于角色的权限检查"""
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # 超级用户拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 检查用户角色权限
        required_permission = getattr(view, 'required_permission', None)
        if required_permission:
            user_permissions = request.user.get_all_permissions()
            return any(perm.codename == required_permission for perm in user_permissions)
        
        return True

class IsOwnerOrReadOnly(permissions.BasePermission):
    """只有所有者可以编辑"""
    
    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        return obj.owner == request.user