#!/usr/bin/env python
"""
项目初始化脚本
运行此脚本来设置数据库和创建超级用户
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ipwtbackend.settings')
    django.setup()

def create_initial_data():
    from accounts.models import Role
    from django.contrib.auth.models import Permission
    
    # 创建基本角色
    admin_role, created = Role.objects.get_or_create(
        name='管理员',
        defaults={'description': '系统管理员，拥有所有权限'}
    )
    
    user_role, created = Role.objects.get_or_create(
        name='普通用户',
        defaults={'description': '普通用户，基本权限'}
    )
    
    # 为管理员角色分配所有权限
    if created:
        admin_role.permissions.set(Permission.objects.all())
    
    print("初始角色创建完成")

def main():
    print("开始初始化Django项目...")
    
    # 创建迁移文件
    print("1. 创建迁移文件...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    
    # 执行迁移
    print("2. 执行数据库迁移...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # 设置Django环境
    setup_django()
    
    # 创建初始数据
    print("3. 创建初始数据...")
    create_initial_data()
    
    # 创建超级用户
    print("4. 创建超级用户...")
    execute_from_command_line(['manage.py', 'createsuperuser'])
    
    print("项目初始化完成！")
    print("你现在可以运行 'python manage.py runserver' 来启动开发服务器")

if __name__ == '__main__':
    main()