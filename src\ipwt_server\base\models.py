from django.db import models


class Camera(models.Model):
    """摄像头基本信息模型"""

    # 相机类型选择
    CAMERA_TYPE_CHOICES = [
        ('main', '主'),
        ('cockpit', '驾驶室'),
        ('other', '其他'),
    ]

    # 相机品牌选择
    CAMERA_BRAND_CHOICES = [
        ('Hikvision', 'Hikvision'),
        ('Dahua', 'Dahua'),
    ]

    # 基本信息字段
    name = models.CharField(
        max_length=32,
        verbose_name='相机名称',
        help_text='相机名称，不能为空'
    )

    camera_type = models.CharField(
        max_length=20,
        choices=CAMERA_TYPE_CHOICES,
        blank=True,
        null=True,
        verbose_name='相机类型',
        help_text='相机类型：主、驾驶室、其他'
    )

    brand = models.CharField(
        max_length=50,
        choices=CAMERA_BRAND_CHOICES,
        blank=True,
        null=True,
        verbose_name='相机品牌',
        help_text='相机品牌'
    )

    ip = models.CharField(
        max_length=256,
        blank=True,
        null=True,
        verbose_name='IP地址',
        help_text='摄像头IP地址'
    )

    port = models.IntegerField(
        blank=True,
        null=True,
        verbose_name='端口',
        help_text='摄像头端口号'
    )

    username = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name='账号',
        help_text='登录账号'
    )

    password = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name='密码',
        help_text='登录密码'
    )

    channel = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name='通道号',
        help_text='摄像头通道号'
    )

    stream_type = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name='码流类型',
        help_text='码流类型'
    )

    rtsp_url = models.CharField(
        max_length=512,
        verbose_name='RTSP地址',
        help_text='根据品牌、IP、端口、账号、密码、通道号、码流类型拼接而成的RTSP地址，不能为空'
    )

    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name='备注',
        help_text='备注信息'
    )

    # 时间戳字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '摄像头'
        verbose_name_plural = '摄像头'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_camera_type_display()})"

    def save(self, *args, **kwargs):
        """保存时自动生成RTSP地址"""
        if not self.rtsp_url and self.can_generate_rtsp():
            self.rtsp_url = self.generate_rtsp_url()
        super().save(*args, **kwargs)

    def can_generate_rtsp(self):
        """检查是否可以生成RTSP地址"""
        return all([
            self.brand,
            self.ip,
            self.port,
            self.username,
            self.password,
            self.channel
        ])

    def generate_rtsp_url(self):
        """根据品牌和参数生成RTSP地址"""
        if not self.can_generate_rtsp():
            return ""

        if self.brand == 'Hikvision':
            # 海康威视RTSP地址格式
            return f"rtsp://{self.username}:{self.password}@{self.ip}:{self.port}/Streaming/Channels/{self.channel}01"
        elif self.brand == 'Dahua':
            # 大华RTSP地址格式
            return f"rtsp://{self.username}:{self.password}@{self.ip}:{self.port}/cam/realmonitor?channel={self.channel}&subtype={self.stream_type or '0'}"
        else:
            return ""
