from django.contrib.auth.models import AbstractUser
from django.db import models

class Role(models.Model):
    """角色模型"""
    name = models.CharField(max_length=50, unique=True, verbose_name='角色名称')
    description = models.TextField(blank=True, verbose_name='角色描述')
    permissions = models.ManyToManyField(
        'auth.Permission',
        blank=True,
        verbose_name='权限'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '角色'
        verbose_name_plural = '角色'

    def __str__(self):
        return self.name

class CustomUser(AbstractUser):
    """扩展用户模型"""
    phone = models.CharField(max_length=20, blank=True, verbose_name='手机号')
    avatar = models.ImageField(upload_to='avatars/', blank=True, verbose_name='头像')
    roles = models.ManyToManyField(Role, blank=True, verbose_name='角色')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def get_all_permissions(self):
        """获取用户所有权限"""
        permissions = set()
        for role in self.roles.all():
            permissions.update(role.permissions.all())
        return permissions