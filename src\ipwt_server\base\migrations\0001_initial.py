# Generated by Django 5.2.4 on 2025-07-30 08:06

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Camera",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="相机名称，不能为空",
                        max_length=32,
                        verbose_name="相机名称",
                    ),
                ),
                (
                    "camera_type",
                    models.Char<PERSON>ield(
                        blank=True,
                        choices=[
                            ("main", "主"),
                            ("cockpit", "驾驶室"),
                            ("other", "其他"),
                        ],
                        help_text="相机类型：主、驾驶室、其他",
                        max_length=20,
                        null=True,
                        verbose_name="相机类型",
                    ),
                ),
                (
                    "brand",
                    models.Char<PERSON>ield(
                        blank=True,
                        choices=[("Hikvision", "Hikvision"), ("Dahua", "Dahua")],
                        help_text="相机品牌",
                        max_length=50,
                        null=True,
                        verbose_name="相机品牌",
                    ),
                ),
                (
                    "ip",
                    models.CharField(
                        blank=True,
                        help_text="摄像头IP地址",
                        max_length=256,
                        null=True,
                        verbose_name="IP地址",
                    ),
                ),
                (
                    "port",
                    models.IntegerField(
                        blank=True,
                        help_text="摄像头端口号",
                        null=True,
                        verbose_name="端口",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        blank=True,
                        help_text="登录账号",
                        max_length=128,
                        null=True,
                        verbose_name="账号",
                    ),
                ),
                (
                    "password",
                    models.CharField(
                        blank=True,
                        help_text="登录密码",
                        max_length=128,
                        null=True,
                        verbose_name="密码",
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        blank=True,
                        help_text="摄像头通道号",
                        max_length=128,
                        null=True,
                        verbose_name="通道号",
                    ),
                ),
                (
                    "stream_type",
                    models.CharField(
                        blank=True,
                        help_text="码流类型",
                        max_length=128,
                        null=True,
                        verbose_name="码流类型",
                    ),
                ),
                (
                    "rtsp_url",
                    models.CharField(
                        help_text="根据品牌、IP、端口、账号、密码、通道号、码流类型拼接而成的RTSP地址，不能为空",
                        max_length=512,
                        verbose_name="RTSP地址",
                    ),
                ),
                (
                    "remarks",
                    models.TextField(
                        blank=True, help_text="备注信息", null=True, verbose_name="备注"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "摄像头",
                "verbose_name_plural": "摄像头",
                "ordering": ["-created_at"],
            },
        ),
    ]
